#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CRF功能测试脚本
用于诊断TorchCRF在训练过程中的问题
"""

import torch
import torch.nn as nn
from TorchCRF import CRF
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_crf_basic():
    """测试CRF基本功能"""
    print("\n" + "=" * 60)
    print("🧪 测试CRF基本功能")
    print("-" * 30)
    
    try:
        # 创建CRF
        num_labels = 6
        crf = CRF(num_labels)
        print(f"✅ CRF创建成功，标签数: {num_labels}")
        
        # 测试前向传播
        batch_size, seq_len = 2, 10
        logits = torch.randn(seq_len, batch_size, num_labels)
        targets = torch.randint(0, num_labels, (seq_len, batch_size))
        mask = torch.ones(seq_len, batch_size, dtype=torch.bool)
        
        print(f"📊 输入维度: logits={logits.shape}, targets={targets.shape}, mask={mask.shape}")
        
        # 测试损失计算
        log_likelihood = crf(logits, targets, mask=mask)
        loss = -log_likelihood.mean() if log_likelihood.dim() > 0 else -log_likelihood
        print(f"✅ 损失计算成功: {loss.item():.4f}")
        
        # 测试解码
        pred = crf.viterbi_decode(logits, mask=mask)
        print(f"✅ Viterbi解码成功: 返回类型={type(pred)}, 长度={len(pred)}")
        
        return True
        
    except Exception as e:
        print(f"❌ CRF测试失败: {e}")
        return False

def test_crf_with_projection():
    """测试CRF与投影层的组合"""
    print("\n" + "=" * 60)
    print("🧪 测试CRF与投影层组合")
    print("-" * 30)
    
    try:
        # 模拟训练场景
        hidden_size = 256
        num_labels = 6
        batch_size, seq_len = 2, 10
        
        # 创建投影层和CRF
        projection = nn.Linear(hidden_size, num_labels)
        crf = CRF(num_labels)
        print(f"✅ 投影层和CRF创建成功")
        
        # 模拟隐藏状态
        hidden_states = torch.randn(batch_size, seq_len, hidden_size)
        print(f"📊 隐藏状态维度: {hidden_states.shape}")
        
        # 投影到标签空间
        logits = projection(hidden_states)
        print(f"📊 投影后维度: {logits.shape}")
        
        # 转换为CRF所需格式 (seq_len, batch_size, num_labels)
        logits_t = logits.transpose(0, 1)
        print(f"📊 转换后维度: {logits_t.shape}")
        
        # 创建目标和掩码
        targets = torch.randint(0, num_labels, (seq_len, batch_size))
        mask = torch.ones(seq_len, batch_size, dtype=torch.bool)
        
        # 测试损失计算
        log_likelihood = crf(logits_t, targets, mask=mask)
        loss = -log_likelihood.mean() if log_likelihood.dim() > 0 else -log_likelihood
        print(f"✅ 损失计算成功: {loss.item():.4f}")
        
        # 测试解码
        pred = crf.viterbi_decode(logits_t, mask=mask)
        print(f"✅ Viterbi解码成功: 返回类型={type(pred)}, 长度={len(pred)}")
        
        # 测试梯度计算
        loss.backward()
        print(f"✅ 梯度计算成功")
        
        return True
        
    except Exception as e:
        print(f"❌ CRF与投影层测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔧 TorchCRF 功能诊断测试")
    print("=" * 60)
    
    # 检查PyTorch和TorchCRF版本
    print(f"🐍 PyTorch版本: {torch.__version__}")
    try:
        import TorchCRF
        print(f"🔧 TorchCRF导入成功")
    except ImportError as e:
        print(f"❌ TorchCRF导入失败: {e}")
        return
    
    # 运行测试
    test1_result = test_crf_basic()
    test2_result = test_crf_with_projection()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("-" * 30)
    print(f"基本功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"投影层组合测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！CRF功能正常")
    else:
        print("⚠️  存在问题，需要进一步调试")

if __name__ == '__main__':
    main()
