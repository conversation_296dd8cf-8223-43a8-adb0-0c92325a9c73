#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评估过程测试脚本
用于验证训练和评估过程是否正常工作
"""

import torch
import torch.nn as nn
from TorchCRF import CRF
from sklearn.metrics import f1_score
import json
import os
from torch.utils.data import Dataset, DataLoader
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplePunctuationDataset(Dataset):
    """简化的标点数据集用于测试"""
    def __init__(self, root_dir: str, char2id_file: str, max_length=64):
        self.max_length = max_length
        with open(char2id_file, encoding='utf-8') as f:
            self.char2id = json.load(f)
        
        text_dir = os.path.join(root_dir, 'text')
        label_dir = os.path.join(root_dir, 'label')
        self.samples = []
        
        # 只加载前10个文件用于快速测试
        files = sorted(os.listdir(text_dir))[:10]
        
        for fname in files:
            if not fname.endswith('.txt'):
                continue
            sid = fname[:-4]
            try:
                with open(os.path.join(text_dir, fname), encoding='utf-8') as ft, \
                     open(os.path.join(label_dir, f'{sid}.txt'), encoding='utf-8') as fl:
                    text = ft.read().strip()
                    labels = list(map(int, fl.read().strip().split()))
                
                if len(text) == len(labels):
                    self.samples.append((text, labels))
            except Exception as e:
                logger.error(f"读取文件 {fname} 失败: {e}")
        
        logger.info(f"加载了 {len(self.samples)} 个样本用于测试")

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        text, labels = self.samples[idx]
        ids = [self.char2id.get(c, self.char2id['<unk>']) for c in text][:self.max_length]
        lbs = labels[:self.max_length]
        length = len(ids)

        # 修正标签范围
        num_labels = 6
        corrected_lbs = []
        for label in lbs:
            if label < 0 or label >= num_labels:
                corrected_lbs.append(1)  # 默认为无标点
            else:
                corrected_lbs.append(label)
        lbs = corrected_lbs

        pad_len = self.max_length - length
        ids += [self.char2id['<pad>']] * pad_len
        lbs += [-100] * pad_len
        
        return torch.tensor(ids, dtype=torch.long), torch.tensor(lbs, dtype=torch.long), length

def collate_fn(batch):
    ids, lbs, lens = zip(*batch)
    ids = torch.stack(ids)
    lbs = torch.stack(lbs)
    lens = torch.tensor(lens)
    return ids, lbs, lens

def test_evaluation_process():
    """测试评估过程"""
    print("\n" + "=" * 60)
    print("🧪 测试评估过程")
    print("-" * 30)
    
    try:
        # 创建简单的模型组件
        hidden_size = 256
        num_labels = 6
        
        projection = nn.Linear(hidden_size, num_labels)
        crf = CRF(num_labels)
        
        # 创建测试数据
        dataset = SimplePunctuationDataset('datasets/val', 'datasets/char2id.json')
        loader = DataLoader(dataset, batch_size=2, shuffle=False, collate_fn=collate_fn)
        
        print(f"✅ 数据加载成功，数据集大小: {len(dataset)}")
        
        # 模拟评估过程
        preds, gts = [], []
        
        for batch_idx, (text_ids, punc, lens) in enumerate(loader):
            if batch_idx >= 3:  # 只测试前3个批次
                break
                
            print(f"📊 处理批次 {batch_idx}: text_ids={text_ids.shape}, punc={punc.shape}, lens={lens}")
            
            # 模拟隐藏状态
            batch_size, seq_len = text_ids.size()
            hidden_states = torch.randn(batch_size, seq_len, hidden_size)
            
            # 投影到标签空间
            logits = projection(hidden_states)
            
            # 处理标签和掩码
            valid_mask = (punc != -100)
            clean_punc = punc.clone()
            
            if valid_mask.any():
                valid_labels = clean_punc[valid_mask]
                out_of_range = (valid_labels < 0) | (valid_labels >= num_labels)
                if out_of_range.any():
                    valid_labels[out_of_range] = 1
                    clean_punc[valid_mask] = valid_labels
            
            clean_punc[~valid_mask] = 0
            
            # CRF解码
            logits_t = logits.transpose(0, 1)  # (seq_len, batch_size, num_labels)
            valid_mask_t = valid_mask.transpose(0, 1)  # (seq_len, batch_size)
            pred = crf.viterbi_decode(logits_t, mask=valid_mask_t)
            
            print(f"✅ CRF解码成功，预测结果类型: {type(pred)}, 长度: {len(pred)}")
            
            # 收集结果
            for i, (p, l) in enumerate(zip(pred, lens)):
                actual_len = min(int(l.item()), len(p))
                batch_valid_mask = valid_mask[i][:actual_len]
                
                if batch_valid_mask.sum() > 0:
                    valid_pred = [p[j] for j in range(actual_len) if batch_valid_mask[j]]
                    valid_true = clean_punc[i][:actual_len][batch_valid_mask].tolist()
                    
                    preds.extend(valid_pred)
                    gts.extend(valid_true)
        
        # 计算F1分数
        if len(gts) > 0:
            f1 = f1_score(gts, preds, average='weighted', zero_division=0)
            print(f"✅ F1分数计算成功: {f1:.4f}")
            print(f"📊 预测样本数: {len(preds)}, 真实样本数: {len(gts)}")
        else:
            print("⚠️  没有有效的预测样本")
        
        return True
        
    except Exception as e:
        print(f"❌ 评估过程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔧 评估过程诊断测试")
    print("=" * 60)
    
    # 检查数据文件是否存在
    if not os.path.exists('datasets/val'):
        print("❌ 验证数据目录不存在")
        return
    
    if not os.path.exists('datasets/char2id.json'):
        print("❌ 字符映射文件不存在")
        return
    
    print("✅ 数据文件检查通过")
    
    # 运行评估测试
    result = test_evaluation_process()
    
    print("\n" + "=" * 60)
    print("📋 测试结果")
    print("-" * 30)
    print(f"评估过程测试: {'✅ 通过' if result else '❌ 失败'}")
    
    if result:
        print("🎉 评估过程工作正常！")
    else:
        print("⚠️  评估过程存在问题，需要进一步调试")

if __name__ == '__main__':
    main()
